import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import * as httpSettings from "@common/api/http";
import { Switch, Table, Select, Skeleton, Button, Popover } from "antd";
import DraggablePopUp from "@components/DraggablePopUp";
import moment from "moment";
import CreateTeamModal, {
  CREATETYPE_UPGRADE,
} from "@components/CreateTeam";
import ProdAuthMbrsDrawer from "../user/ProdAuthMbrsDrawer";
import MbrAuthProdsDrawer from "../user/MbrAuthProdsDrawer";
import { CloudDownloadBuy } from "@/settings/utils/CloudDownloadDraggable";
import ProductVip from "../../../personal/assets/images/productVip.png";
import ProductVip_x from "../../../personal/assets/images/productVip_x.png";
import { eOrderStatus, eProductGroupId, eProductId, eProductStatus } from "@common/utils/enum";

const { Column, ColumnGroup } = Table;

export default function Settings_Product({ teamId: propsTeamId, productId: propsProductId, visible: propsVisible }) {
  //const dispatch = useDispatch();
  const navigate = useNavigate();
  const paramsTeamId = useParams().teamId;
  const teamId = propsTeamId || paramsTeamId; // 优先使用props，否则使用路由参数
  const paramsProductId = useParams().productId;
  const _prodId = propsProductId || paramsProductId; // 优先用props，其次用路由
  const [productsLoading, setProductsLoading] = useState(false);
  const [groupList, setGroupList] = useState([]);
  const [productsForUi, setProductsForUi] = useState([]); //UI呈现的产品列表(会有类型过滤导致显示的产品列表有筛选)
  const [productsFinal, setProductsFinal] = useState([]); //实际完整的产品列表
  const [createTeamModalVisible, setCreateTeamModalVisible] = useState(false);
  const [typeValue, setTypeValue] = useState(0);
  const [listHeight, setListHeight] = useState(0);
  const [prodAuthMbrsDrawerVisible, setProdAuthMbrsDrawerVisible] = useState(false); //显示授权穿梭框
  const [currentAuthProd, setCurrentAuthProd] = useState(null); //"当前"点击的授权应用
  const [historyVisible, setHistoryVisible] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [historyList, setHistoryList] = useState([]);
  const [historyItem, setHistoryItem] = useState(null);
  const [cloudBuyVisible, setCloudBuyVisible] = useState(false);
  const [tipsShow, setTipsShow] = useState(false);
  const [expireVisible, setExpireVisible] = useState(false);
  const [expireItem, setExpireItem] = useState(null);
  const [productId, setProductId] = useState(_prodId);
  const [allUsers, setAllUsers] = useState([]); // 所有用户列表
  const [authDrawerVisible, setAuthDrawerVisible] = useState(false); // 成员授权应用抽屉
  const [currentMember, setCurrentMember] = useState(null); // 当前选中的成员

  useEffect(() => {
    loadTeamProductList();
    loadUserList();
  }, []);

  useEffect(() => {
    changeListHeight();
    window.addEventListener("resize", changeListHeight);
    return () => {
      window.removeEventListener("resize", changeListHeight);
    };
  }, []);

  // 最大高度
  const changeListHeight = () => {
    // topbar、标题、页面header、页面footer、table表头页脚
    let listHeight = document.documentElement.clientHeight - 320;
    setListHeight(listHeight);
  };

  //20250621 跳转到产品页时，打开授权对话框
  useEffect(() => {
    if(productId && productsFinal.length > 0 && propsVisible) {
      let item = productsFinal.filter(i => i.productId == productId);
      if(paramsProductId) { //代表是在 Settings Page形态，而不是 Drawer
        const newUrl = `/#/${teamId}/settings/product`; //移除 productId
        window.history.replaceState({}, '', newUrl);
        setProductId(null);
      }
      if(item.length > 0)  //打开授权产品对话框
        authProductToMembers(item[0]);
    }
  },[_prodId,productsFinal,propsVisible]);

  //加载授权产品列表
  async function loadTeamProductList() {
    setProductsLoading(true);
    await httpSettings
      .team_711_get_team_product_list({ teamId: teamId })
      .then((res) => {
        if (res.resultCode == 200) {
          productListFormat(res.productList || []);
        }
      });
    setProductsLoading(false);
  }

  // 加载用户列表
  async function loadUserList() {
    try {
      const res = await httpSettings.setting_202_get_team_allusergrp({
        teamId: teamId
      });
      
      if (res.resultCode === 200) {
        const users = res.userList || [];
        setAllUsers(users);
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
    }
  }

  function productListFormat(productList = []) {
    let _prodList = [];
    productList.forEach((product) => {
      let item = _prodList.find(
        (_product) => product.groupId == _product.groupId
      );
      if (!item) {
        _prodList.push({
          groupName: product.groupName,
          groupId: product.groupId,
          products: [product],
        });
      } else {
        item.products.push(product);
      }
    });
    setGroupList([..._prodList]);
    let _allProds = [];
    _prodList.forEach((group) => {
      let groupList = group.products.map((_prod, index) => ({
        ..._prod,
        key: _prod.productId,
        isRowSpan: index == 0 ? true : false,
        groupListLength: index == 0 ? group.products.length : 0,
        authCntDesc: !!_prod.authCntDesc ?
          _prod.authCntDesc : productsFinal.find((product) => product.productId == _prod.productId)?.authCntDesc || "",
        objCntDesc: !!_prod.objCntDesc ?
          _prod.objCntDesc : productsFinal.find((product) => product.productId == _prod.productId)?.objCntDesc || "",
      }));
      _allProds = _allProds.concat(groupList);
    });

    setProductsForUi([..._allProds]);
    setProductsFinal([..._allProds]);
  }

  //启用/禁用某个应用
  function setToggle(productId, enableFlg) {
    httpSettings
      .team_712_toggle_product({
        teamId: teamId,
        productId: productId,
        enableFlg: enableFlg,
      })
      .then((res) => {
        if (res.resultCode == 200) {
          productListFormat(res.productList || []);
        }
      });
  }

  function authProductToMembers(_product) {  //打开授权对话框
    setCurrentAuthProd(_product);
    setProdAuthMbrsDrawerVisible(true);
  }

  function reGetData() {
    loadTeamProductList();
    setCreateTeamModalVisible(false);
    setTipsShow(true);
  }

  //显示到期时间
  function timeFormat(item) {
    let time = item?.expirationDt;
    return item?.freeFlg == 1 ? "∞"
      : time ? time == "2099-12-31 23:59:59" ? "∞"
        : moment(time).format("YYYY-MM-DD")
      : "-";
  }

  function lineColorFormat(item) {
    if (
      item.enableFlg != 1 ||
      item.statusType == eProductStatus.Status_3_Unreleased //即将推出
    ) {
      return "#AAAAAA";
    }
    return "#000";
  }

  function lineColorXFormat(item, dataIndex) {
    if (
      item.productId != eProductId.Pid_11_Explorer && item.productId != eProductId.Pid_12_Space &&
      item.freeFlg == 0 &&
      !!item.expirationDt && moment().isAfter(moment(item.expirationDt))
    ) {
      //已过期
      if (item.enableFlg == 1 && dataIndex == "expirationDt") {
        return "red"; //未禁用
      }
      return "#AAAAAA";
    }
    return lineColorFormat(item);
  }

  function operationFormat(item) {
    if (
      item.productId == eProductId.Pid_11_Explorer || item.productId == eProductId.Pid_12_Space ||
      item.statusType == eProductStatus.Status_3_Unreleased
    ) {
      return <div>-</div>;
    }
    // if(item.statusType == eProductStatus.Status_3_Unreleased ){ //即将推出
    //   return (
    //     <>
    //       <div style={{width:147}}/>
    //       <div style={{width:50,display:'flex',justifyContent:'center'}}>
    //         <Switch size="small"
    //           checkedChildren="启用"
    //           unCheckedChildren="禁用"
    //           checked={item.enableFlg == 1}
    //           onChange={() => item.enableFlg == 1 ? setToggle(item.productId, 0) : setToggle(item.productId, 1)}
    //         />
    //       </div>
    //     </>
    //   );
    // }
    return (
      <>
        {/*第1列 购买/续费*/}
        <div style={{ width: 50, textAlign: "center" }}>
          {item.freeFlg == 0 &&
          !!item.expirationDt &&
          moment().isAfter(moment(item.expirationDt)) ? ( //已过期
            <a
              onClick={() =>
                item.productId != eProductId.Pid_13_Cdisk
                  ? setCreateTeamModalVisible(true)
                  : setCloudBuyVisible(true)
              }
              style={
                item.enableFlg == 1
                  ?
                  { color: "#fff", backgroundColor: "red", borderRadius: 5, padding: "2px 8px", }
                  :
                  { color: "#fff", backgroundColor: "#aaa", borderRadius: 5, padding: "2px 8px", }
              }
            >
              续费
            </a>
          ) : (
            <a
              onClick={() =>
                item.productId != eProductId.Pid_13_Cdisk ?
                  setCreateTeamModalVisible(true) : setCloudBuyVisible(true)
              }
            >
              购买
            </a>
          )}
        </div>
        {/*第2列 购买历史*/}
        <div style={{ margin: "0px 5px" }}>|</div>
        <a
          style={{ width: 70, display: "flex", justifyContent: "center" }}
          onClick={() => {
            setHistoryVisible(true);
            getHistoryList(item);
            setHistoryItem(item);
          }}
        >
          购买历史
        </a>
        <div style={{ margin: "0px 5px" }}>|</div>
        {/*第3列 启用/禁用*/}
        <div style={{ width: 60, display: "flex", justifyContent: "center" }}>
          <Switch
            size="small"
            disabled={
              //item.enableFlg != 1 ||   //20250523 Jim 注释掉，不然无法再开启 tmsbug-12291
              item.statusType == eProductStatus.Status_3_Unreleased
            }
            checkedChildren="启用"
            unCheckedChildren="禁用"
            checked={item.enableFlg == 1}
            onChange={() =>
              item.enableFlg == 1
                ?
                setToggle(item.productId, 0)
                :
                setToggle(item.productId, 1)
            }
          />
        </div>
      </>
    );
  }

  //单个“应用”的购买历史
  async function getHistoryList(item) {
    setHistoryLoading(true);
    await httpSettings
      .team_722_get_order_list_by_product_id({
        teamId,
        productId: item.productId,
      })
      .then((res) => {
        if (res.resultCode == 200) {
          let list = (res.orderList || []).map((history) => {
            history.key = history.id;
            history.status = history.statusType == eOrderStatus.Status_1_Paid ? "已支付" : "未支付";
            return history;
          });
          setHistoryList([...list]);
        }
      });
    setHistoryLoading(false);
  }

  function expireVisibleChange(visible, item) {
    setExpireVisible(visible);
    setExpireItem(item);
  }

  const columns = [
    {
      title: (
        <div style={{ display: "flex", justifyContent: "center" }}>类别</div>
      ),
      dataIndex: "groupName",
      key: "groupName",
      render: (groupName, item) => (
        <div style={{ display: "flex", justifyContent: "center" }}>
          {groupName}
        </div>
      ),
      onCell: (item) => {
        if (item.isRowSpan) {
          return { rowSpan: item.groupListLength };
        } else {
          return { rowSpan: 0 };
        }
      },
    },
    {
      title: (
        <div style={{ display: "flex", justifyContent: "center" }}>应用</div>
      ),
      dataIndex: "productName",
      key: "productName",
      render: (productName, item) => (
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: lineColorFormat(item),
          }}
        >
          {productName}
          {item.statusType == eProductStatus.Status_2_QA && (
            <span style={{ color: "#70B603", fontSize: 12, marginLeft: 10 }}>
              内测中
            </span>
          )}
          {item.statusType == eProductStatus.Status_3_Unreleased && (
            <span style={{ color: "#F59A23", fontSize: 12, marginLeft: 10 }}>
              即将推出
            </span>
          )}
        </div>
      ),
      onCell: (item) => {
        return { rowSpan: 1 };
      },
    },
    {
      title: (
        <div style={{ display: "flex", justifyContent: "center" }}>
          有效期至
        </div>
      ),
      dataIndex: "expirationDt",
      key: "expirationDt",
      render: (expirationDt, item) => {
        if (
          item.productId != eProductId.Pid_11_Explorer &&
          item.productId != eProductId.Pid_12_Space &&
          item.freeFlg == 0 &&
          !!item.expirationDt &&
          moment().isAfter(moment(item.expirationDt))
        ) {
          return (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              title={`应用'${item.productName}'Vip已过期，请续费`}
            >
              <a
                onClick={() => {
                  item.productId == eProductId.Pid_13_Cdisk
                    ? setCloudBuyVisible(true)
                    : setCreateTeamModalVisible(true);
                }}
                style={{ color: lineColorXFormat(item, "expirationDt") }}
              >
                {timeFormat(item)}
              </a>
            </div>
          );
        }
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <div style={{ color: lineColorXFormat(item) }}>
              {timeFormat(item)}
            </div>
          </div>
        );
      },
      onCell: (item) => {
        return { rowSpan: 1 };
      },
    },
    {
      title: (
        <div style={{ display: "flex", justifyContent: "center" }}>
          应用版本
        </div>
      ),
      dataIndex: "freeFlg",
      key: "freeFlg",
      render: (freeFlg, item) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            color: lineColorFormat(item),
          }}
        >
          {item.groupId == eProductGroupId.Pgid_1_OS && item.productId != eProductId.Pid_13_Cdisk ? (
            "-"
          ) : freeFlg == 0 ? (
            !!item.expirationDt &&
            moment().isAfter(moment(item.expirationDt)) ? (
              <a
                style={{ color: "inherit" }}
                onClick={() =>
                  item.productId != eProductId.Pid_13_Cdisk
                    ? setCreateTeamModalVisible(true)
                    : setCloudBuyVisible(true)
                }
              >
                <img style={{ height: 22 }} src={ProductVip_x} />
              </a>
            ) : (
              <img style={{ height: 22 }} src={ProductVip} />
            )
          ) : (
            "基础版"
          )}
        </div>
      ),
      onCell: (item) => {
        return { rowSpan: 1 };
      },
    },
    {
      title: (
        <Popover
          open={tipsShow}
          placement="bottom"
          trigger={"click"}
          title={
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <span style={{ fontWeight: "bold", fontSize: 16 }}>提示</span>
              <a
                style={{ marginLeft: 20, color: "#666" }}
                onClick={() => setTipsShow(false)}
              >
                <span className="iconfont guanbi" style={{ fontSize: 16 }} />
              </a>
            </div>
          }
          content={
            <div style={{ textAlign: "center" }}>
              <div>只有授权了的成员才能正常使用Vip应用</div>
              <div>您购买了Vip应用，可点击“已授权/总授权数”列的</div>
              <div>蓝色链接，进行应用授权</div>
              <div style={{ marginTop: 30 }}>
                <Button
                  type={"primary"}
                  style={{ borderRadius: 5 }}
                  onClick={() => setTipsShow(false)}
                >
                  我知道了
                </Button>
              </div>
            </div>
          }
        >
          <div style={{ display: "flex", justifyContent: "center" }}>
            已授权/总授权数
          </div>
        </Popover>
      ),
      dataIndex: "authCntDesc",
      key: "authCntDesc",
      render: (authCntDesc, _product) => {
        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            <div style={{ flex: 0.5 }} />
            <div style={{ flex: 1, display: "flex", justifyContent: "center" }}>
              {_product.groupId != eProductGroupId.Pgid_1_OS ? (
                <a
                  onClick={() => {
                    !!_product.expirationDt &&
                    moment().isAfter(moment(_product.expirationDt))
                      ? expireVisibleChange(true, _product)
                      : authProductToMembers(_product);
                  }}
                >
                  {/*{item.freeFlg == 0 ? (authCntDesc || "") : `${item.authUserCnt||0}人`}*/}
                  {authCntDesc || ""}
                </a>
              ) : (
                <div>-</div>
              )}
            </div>
            <div style={{ flex: 0.5, display: "flex", justifyContent: "end" }}>
              {_product.groupId != eProductGroupId.Pgid_1_OS &&
              _product.freeFlg == 0 &&
              !!_product.expirationDt &&
              moment().isAfter(moment(_product.expirationDt)) &&
              _product.authUserCnt > _product.authCnt ? (
                <a
                  style={
                    _product.enableFlg == 1 ? { color: "red" } : { color: "#aaa" }
                  }
                  onClick={() => expireVisibleChange(true, _product)}
                  className="iconfont guoqitishi"
                  title={`应用'${_product.productName}'Vip已过期，请续费\n授权人数超出授权额度，请检查。`}
                />
              ) : _product.productId != eProductId.Pid_11_Explorer &&
                _product.productId != eProductId.Pid_12_Space &&
                _product.freeFlg == 0 &&
                !!_product.expirationDt &&
                moment().isAfter(moment(_product.expirationDt)) ? (
                <a
                  style={
                    _product.enableFlg == 1 ? { color: "red" } : { color: "#aaa" }
                  }
                  onClick={() => expireVisibleChange(true, _product)}
                  className="iconfont guoqitishi"
                  title={`应用'${_product.productName}'Vip已过期，请续费`}
                />
              ) : _product.authUserCnt > _product.authCnt &&
                _product.freeFlg == 0 &&
                _product.groupId != eProductGroupId.Pgid_1_OS ? (
                <a
                  style={{ color: "#F59A23" }}
                  onClick={() => authProductToMembers(_product)}
                  title={`授权人数超出授权额度，请检查。`}
                  className="iconfont guoqitishi"
                />
              ) : null}
            </div>
          </div>
        );
      },
      onCell: (item) => {
        return { rowSpan: 1 };
      },
    },
    {
      title: (
        <div style={{ display: "flex", justifyContent: "center" }}>
          已使用/总量
        </div>
      ),
      dataIndex: "objCntDesc",
      key: "objCntDesc",
      render: (objCntDesc, item) => {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div
              title={
                (item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk)
                  ? (objCntDesc == "-"
                    ? ""
                    : objCntDesc)
                  : item.totalCntDesc
              }
              className="tms-text-overflow"
              style={{ color: lineColorXFormat(item) }}
            >
              {usedFormat((item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk)
                  ? (objCntDesc == "-"
                    ? ""
                    : objCntDesc)
                  : item.totalCntDesc)}
            </div>
            {item.objCntWarningFlg == 1 &&
            (item.freeFlg == 1 || item.productId == eProductId.Pid_13_Cdisk) ? (
              <a
                style={{ color: "#F59A23" }}
                onClick={() =>
                  item.productId != eProductId.Pid_13_Cdisk
                    ? setCreateTeamModalVisible(true)
                    : setCloudBuyVisible(true)
                }
                title={`应用'${item.productName}'免费额度已用完，请购买Vip版本`}
                className="iconfont guoqitishi"
              />
            ) : null}
          </div>
        );
      },
      onCell: (item) => {
        return { rowSpan: 1 };
      },
    },
    {
      title: (
        <div style={{ display: "flex", justifyContent: "center" }}>操作</div>
      ),
      dataIndex: "operation",
      key: "operation",
      width: 230,
      render: (operation, item) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {operationFormat(item)}
        </div>
      ),
      onCell: (item) => {
        return { rowSpan: 1 };
      },
    },
  ];

  function usedFormat(str = "") {
    let singleList = str.split("");
    let count = singleList.filter((single) => single == "∞").length;
    if (count == 1) {
      return (
        <span>
          {str.substring(0, str.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str.substring(str.indexOf("∞") + 1, str.length)}
        </span>
      );
    }
    if (count == 2) {
      let str2d = str.substring(str.indexOf("∞") + 1, str.length);
      return (
        <span>
          {str.substring(0, str.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str2d.substring(0, str2d.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str2d.substring(str2d.indexOf("∞") + 1, str2d.length)}
        </span>
      );
    }
    if (count == 3) {
      let str2d = str.substring(str.indexOf("∞") + 1, str.length);
      let str3d = str2d.substring(str2d.indexOf("∞") + 1, str2d.length);
      return (
        <span>
          {str.substring(0, str.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str2d.substring(0, str2d.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str3d.substring(0, str3d.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str3d.substring(str3d.indexOf("∞") + 1, str3d.length)}
        </span>
      );
    }
    if (count == 4) {
      let str2d = str.substring(str.indexOf("∞") + 1, str.length);
      let str3d = str2d.substring(str2d.indexOf("∞") + 1, str2d.length);
      let str4d = str3d.substring(str3d.indexOf("∞") + 1, str3d.length);
      return (
        <span>
          {str.substring(0, str.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str2d.substring(0, str2d.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str3d.substring(0, str3d.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str4d.substring(0, str4d.indexOf("∞"))}
          <span style={{ fontSize: 16 }}>∞</span>
          {str4d.substring(str4d.indexOf("∞") + 1, str4d.length)}
        </span>
      );
    }
    return str;
  }

  //应用类型下拉选择，切换类型
  function onProductTypeFilterChanged(value) {
    setTypeValue(value);
    let filterList =
      value == 0
        ? productsFinal
        : productsFinal.filter((product) => product.groupId == value);
    setProductsForUi([...filterList]);
  }

  function addMonthCntFormat(addMonthCnt) {
    return addMonthCnt == 6 ? "半年"
      : addMonthCnt == 12 ? "1年"
      : addMonthCnt == 24 ? "2年"
      : addMonthCnt == 36 ? "3年"
      : addMonthCnt == 0
      ? "-"
      : addMonthCnt.toString() + "个月";
  }

  function orderNoFormat(item) {
    if (!!item.orderDt) {
      return moment(item.orderDt).format("YYYYMMDDHHmmss");
    }
    if (!!item.paidDt) {
      return moment(item.paidDt).format("YYYYMMDDHHmmss");
    }
    return "-";
  }

  function orderNoClick(item) {
    window.open(window.location.origin + `/#/personal/myorder/${item.orderId}`);
  }

  function sort(a, b) {
    let _a = orderNoFormat(a);
    let _b = orderNoFormat(b);
    if (_a != "-" && _b != "-") {
      return parseInt(_a) - parseInt(_b);
    }
    if (_a != "-" && _b == "-") {
      return parseInt(_a) - 0;
    }
    if (_a == "-" && _b != "-") {
      return 0 - parseInt(_b);
    }
    return 0;
  }

  function onClose(saveFlg) {
    setProdAuthMbrsDrawerVisible(false); //关闭授权弹出框
  }

  return (
    <React.Fragment>
      <Skeleton loading={productsLoading}>
        <div
          style={{ padding: "10px 0px 5px 20px", fontSize: 14, height: "100%" }}
          className="product-set"
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              应用类别：
              <Select
                dropdownMatchSelectWidth={false}
                value={typeValue}
                style={{ marginLeft: 10 }}
                onChange={onProductTypeFilterChanged}
              >
                <Select.Option value={0}>全部</Select.Option>
                {groupList.map((group) => {
                  return (
                    <Select.Option value={group.groupId}>
                      {group.groupName}
                    </Select.Option>
                  );
                })}
              </Select>
            </div>
            {!!productsFinal.find((product) => product.freeFlg == 0) && (
              <div style={{ paddingRight: 20 }}>
                <a
                  onClick={() => {
                    navigate(`/personal/myorder`);
                  }}
                >
                  查看订单
                </a>
              </div>
            )}
          </div>

          <div style={{ width: "100%", marginTop: 10 }}>
            <Table
              size="small"
              className="small-table"
              bordered
              style={{ paddingRight: 20 }}
              pagination={false}
              scroll={{
                // y: "calc(100vh - 160px)", // 不支持calc https://github.com/ant-design/ant-design/issues/31909
                y: listHeight,
              }}
              columns={columns}
              dataSource={productsForUi}
            />
          </div>
          <div style={{ height: 80 }}>
            <div
              style={{
                marginTop: 10,
                height: 20,
                fontSize: 12,
                color: "#999",
                visibility: "visible",
              }}
            >
              应用依赖关系备注：
            </div>
            <div
              style={{
                marginLeft: 10,
                height: 20,
                fontSize: 12,
                color: "#999",
                visibility: "visible",
              }}
            >
              1. 报表、系统订阅(依赖于高级搜索)，仪表板(依赖于高级搜索+报表)
            </div>
            <div
              style={{
                marginLeft: 10,
                height: 20,
                fontSize: 12,
                color: "#999",
                visibility: "visible",
              }}
            >
              2. 试卷(依赖于题库)，考试/作业(依赖于试卷+题库)
            </div>
          </div>
        </div>
      </Skeleton>
      <CreateTeamModal
        teamId={teamId}
        type={CREATETYPE_UPGRADE}
        visible={createTeamModalVisible}
        onCancel={() => setCreateTeamModalVisible(false)}
        onOk={() => reGetData()}
        productList={productsFinal}
      />
      {!!cloudBuyVisible && (
        <CloudDownloadBuy
          visible={cloudBuyVisible}
          teamId={teamId}
          onCancel={() => setCloudBuyVisible(false)}
          reGet={() => {
            loadTeamProductList();
            setCloudBuyVisible(false);
          }}
        />
      )}
      {!!currentAuthProd && (
        <ProdAuthMbrsDrawer
          teamId={teamId}
          currentAuthProd={currentAuthProd}
          allUsers={allUsers}
          prodAuthMbrsDrawerVisible={prodAuthMbrsDrawerVisible}
          setProdAuthMbrsDrawerVisible={setProdAuthMbrsDrawerVisible}
          loadTeamMemberList={loadTeamProductList}
          onOpenMemberAuthDrawer={(member) => {
            // 切换到成员授权应用抽屉
            setCurrentMember(member);
            setAuthDrawerVisible(true);
            setProdAuthMbrsDrawerVisible(false);
          }}
        />
      )}
      {!!currentMember && (
        <MbrAuthProdsDrawer
          teamId={teamId}
          currentMember={currentMember}
          authProdList={currentMember?.authProductList}
          loadTeamMemberList={loadTeamProductList}
          authDrawerVisible={authDrawerVisible}
          setAuthDrawerVisible={setAuthDrawerVisible}
          allUsers={allUsers}
          onMemberChange={(member) => {
            setCurrentMember(member);
          }}
        />
      )}
      <DraggablePopUp
        className="tms-modal"
        title={`购买历史-${historyItem?.productName}`}
        open={historyVisible}
        centered
        width={1000}
        onCancel={() => setHistoryVisible(false)}
        footer={
          <div style={{ display: "flex", justifyContent: "center" }}>
            <Button
              type={"primary"}
              style={{ borderRadius: 5 }}
              onClick={() => setHistoryVisible(false)}
            >
              我知道了
            </Button>
          </div>
        }
      >
        <Skeleton loading={historyLoading}>
          <Table
            size="small"
            className="before-header"
            bordered
            dataSource={historyList}
            showSorterTooltip={false}
            pagination={{
              position: ["bottomCenter"],
              size: "small",
              pageSize: 10,
              showQuickJumper: true,
              showSizeChanger: false,
              total: historyList.length,
              showTotal: (total) => {
                return `共${total}条`;
              },
            }}
          >
            <Column
              title={"#"}
              dataIndex={"seqNo"}
              key={"seqNo"}
              render={(seqNo, item, index) => <div>{index + 1}</div>}
            />
            <Column
              title={"订单号"}
              dataIndex={"orderNo"}
              key={"orderNo"}
              render={(orderNo, item) =>
                item.ownerFlg == 1 && (!!item.orderDt || !!item.paidDt) ? (
                  <a onClick={() => orderNoClick(item)}>
                    {orderNoFormat(item)}
                  </a>
                ) : (
                  <div>{orderNoFormat(item)}</div>
                )
              }
            />
            <ColumnGroup title={"购买前"} className="top-header-a">
              <Column
                title={historyItem?.productId == eProductId.Pid_13_Cdisk ? "下载流量" : "授权数"}
                dataIndex={"authCntBefore"}
                key={"authCntBefore"}
                className="top-header-a"
                render={(authCntBefore) =>
                  historyItem?.productId == eProductId.Pid_13_Cdisk ? (
                    <div>{authCntBefore}G</div>
                  ) : authCntBefore > 0 ? (
                    <div>{authCntBefore}人</div>
                  ) : (
                    <div>-</div>
                  )
                }
              />
              <Column
                title={"有效期至"}
                dataIndex={"expirationDtBefore"}
                key={"expirationDtBefore"}
                className="top-header-a"
                render={(expirationDtBefore) => (
                  <div>
                    {!!expirationDtBefore
                      ? moment(expirationDtBefore).format("YYYY-MM-DD")
                      : "-"}
                  </div>
                )}
              />
            </ColumnGroup>
            <ColumnGroup title={"规格选择"} className="top-header-b">
              <Column
                title={historyItem?.productId == eProductId.Pid_13_Cdisk ? "下载流量" : "增/减员数"}
                dataIndex={"adjustAuthCnt"}
                key={"adjustAuthCnt"}
                className="top-header-b"
                render={(adjustAuthCnt) =>
                  historyItem?.productId == eProductId.Pid_13_Cdisk ? (
                    <div>{adjustAuthCnt}G</div>
                  ) : (
                    <div>
                      {adjustAuthCnt < 0 ? "" : "+"}
                      {adjustAuthCnt || 0}人
                    </div>
                  )
                }
              />
              <Column
                title={"购买时长"}
                dataIndex={"adjustMonthCnt"}
                key={"adjustMonthCnt"}
                className="top-header-b"
                render={(adjustMonthCnt) => (
                  <div>{addMonthCntFormat(adjustMonthCnt)}</div>
                )}
              />
            </ColumnGroup>
            <ColumnGroup title={"购买结果"} className="top-header-c">
              <Column
                title={historyItem?.productId == eProductId.Pid_13_Cdisk ? "下载流量" : "授权数"}
                dataIndex={"authCnt"}
                key={"authCnt"}
                className="top-header-c"
                render={(authCnt) =>
                  historyItem?.productId == eProductId.Pid_13_Cdisk ? (
                    <div>{authCnt}G</div>
                  ) : (
                    <div>{authCnt}人</div>
                  )
                }
              />
              <Column
                title={"有效期至"}
                dataIndex={"expirationDt"}
                key={"expirationDt"}
                className="top-header-c"
                render={(expirationDt) => (
                  <div>
                    {!!expirationDt
                      ? moment(expirationDt).format("YYYY-MM-DD")
                      : "-"}
                  </div>
                )}
              />
            </ColumnGroup>
            <Column
              title={"下单人"}
              dataIndex={"creatorName"}
              key={"creatorName"}
              render={(creatorName) => (
                <div>{!!creatorName ? creatorName : "-"}</div>
              )}
            />
            <Column
              title={"下单时间"}
              dataIndex={"orderDt"}
              key={"orderDt"}
              render={(orderDt) => (
                <div>
                  {!!orderDt
                    ? moment(orderDt).format("YYYY-MM-DD HH:mm:ss")
                    : "-"}
                </div>
              )}
              sorter={sort}
              defaultSortOrder={"descend"}
            />
            <Column
              title={"状态"}
              dataIndex={"status"}
              key={"status"}
              render={(status, item) => <div>{status}</div>}
            />
          </Table>
        </Skeleton>
      </DraggablePopUp>
      <DraggablePopUp
        className="tms-modal"
        title="应用过期提示"
        open={expireVisible && !!expireItem}
        centered
        width={400}
        maskClosable={false}
        onCancel={() => expireVisibleChange(false, null)}
        footer={
          <div style={{ display: "flex", justifyContent: "center" }}>
            <Button
              style={{ borderRadius: 5 }}
              onClick={() => expireVisibleChange(false, null)}
            >
              我知道了
            </Button>
            <Button
              type={"primary"}
              style={{ borderRadius: 5 }}
              onClick={() => {
                expireItem?.productId == eProductId.Pid_13_Cdisk
                  ? setCloudBuyVisible(true)
                  : setCreateTeamModalVisible(true);
                expireVisibleChange(false, null);
              }}
            >
              续费
            </Button>
          </div>
        }
      >
        <div style={{ textAlign: "center" }}>
          应用({expireItem?.productName})有效期至{timeFormat(expireItem)}
          ，已过期
        </div>
      </DraggablePopUp>
    </React.Fragment>
  );
}
