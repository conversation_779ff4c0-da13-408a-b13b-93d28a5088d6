# 版本更新功能使用指南

## 功能概述

本功能实现了前端版本更新时在弹窗中显示更新列表内容的功能。当检测到新版本时，会显示一个包含详细更新内容的弹窗。

## 实现的功能

1. **版本检查**: 自动检查版本更新（生产环境下每15秒检查一次）
2. **更新日志显示**: 在更新弹窗中显示详细的更新内容
3. **配置文件管理**: 通过独立的配置文件管理更新日志

## 文件结构

```
tms-task/
├── public/
│   └── changelog.json                    # 更新日志配置文件
├── src/
│   └── common/
│       ├── api/
│       │   └── http_local.js            # 本地API接口（已修改）
│       └── components/
│           └── CheckAppUpdate.jsx       # 版本更新组件（已修改）
└── webpack/
    └── versionPlugin.js                 # Webpack版本插件（已修改）
```

## 配置文件格式

`public/changelog.json` 文件格式：

```json
{
  "versions": {
      "releaseDate": "2024-01-15",
      "features": [
        "新增版本更新提示功能",
        "优化用户界面交互体验"
      ],
      "improvements": [
        "提升系统性能和稳定性",
        "优化页面加载速度"
      ],
      "bugfixes": [
        "修复登录状态异常问题",
        "解决部分浏览器兼容性问题"
      ]
    }
}
```

## 更新日志类型

- **features**: 新功能（绿色图标 ✨）
- **improvements**: 优化改进（蓝色图标 🚀）
- **bugfixes**: 问题修复（红色图标 🐛）

## 如何添加新版本的更新日志

1. 编辑 `public/changelog.json` 文件
2. 在 `versions` 对象中添加新版本号作为键
3. 为新版本添加相应的更新内容
4. 重新构建项目

## 弹窗显示效果

当检测到新版本时，会显示包含以下内容的弹窗：
- 当前版本和最新版本对比
- 按类型分组的更新内容列表
- 发布时间信息
- "立即更新"和"暂不更新"按钮

## 注意事项

1. 只在生产环境下启用版本检查
2. 更新日志文件需要与版本号保持同步
3. 如果没有对应版本的更新日志，弹窗将不显示更新内容部分
4. 版本比较基于数字比较，确保版本号格式正确

## 测试方法

1. 修改 `public/changelog.json` 中的版本号，使其高于当前版本
2. 在生产环境下运行项目
3. 等待版本检查触发或手动刷新页面
4. 观察更新弹窗是否正确显示更新内容
